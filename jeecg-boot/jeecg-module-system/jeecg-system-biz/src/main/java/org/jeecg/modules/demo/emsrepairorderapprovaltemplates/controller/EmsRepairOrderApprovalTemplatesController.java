package org.jeecg.modules.demo.emsrepairorderapprovaltemplates.controller;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.map.MapUtil;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.emsdepartrole.entity.EmsDepartRole;
import org.jeecg.modules.demo.emsdepartrole.service.IEmsDepartRoleService;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.entity.EmsRepairOrderApprovalTemplates;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.service.IEmsRepairOrderApprovalTemplatesService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.demo.emsrepairorderapprovaltemplatesteps.entity.EmsRepairOrderApprovalTemplateSteps;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplatesteps.service.IEmsRepairOrderApprovalTemplateStepsService;
import org.jeecg.modules.system.util.SecurityUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
 /**
 * @Description: 审批模板表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
@Tag(name="审批模板表")
@RestController
@RequestMapping("/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates")
@Slf4j
public class EmsRepairOrderApprovalTemplatesController extends JeecgController<EmsRepairOrderApprovalTemplates, IEmsRepairOrderApprovalTemplatesService> {
	@Autowired
	private IEmsRepairOrderApprovalTemplatesService emsRepairOrderApprovalTemplatesService;

	@Autowired
	private IEmsRepairOrderApprovalTemplateStepsService emsRepairOrderApprovalTemplateStepsService;

	@Autowired
	private IEmsDepartRoleService emsDepartRoleService;

	/**
	 * 分页列表查询
	 *
	 * @param emsRepairOrderApprovalTemplates
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "审批模板表-分页列表查询")
	@Operation(summary="审批模板表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<EmsRepairOrderApprovalTemplates>> queryPageList(EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		try {
			log.info("查询审批模板列表，pageNo: {}, pageSize: {}", pageNo, pageSize);

			QueryWrapper<EmsRepairOrderApprovalTemplates> queryWrapper = QueryGenerator.initQueryWrapper(emsRepairOrderApprovalTemplates, req.getParameterMap());
			Page<EmsRepairOrderApprovalTemplates> page = new Page<EmsRepairOrderApprovalTemplates>(pageNo, pageSize);
			IPage<EmsRepairOrderApprovalTemplates> pageList = emsRepairOrderApprovalTemplatesService.page(page, queryWrapper);

			log.info("查询到 {} 条审批模板记录", pageList.getTotal());
			return Result.OK(pageList);
		} catch (Exception e) {
			log.error("查询审批模板列表异常", e);
			return Result.error("查询失败：" + e.getMessage());
		}
	}

	/**
	 *   添加
	 *
	 * @param emsRepairOrderApprovalTemplates
	 * @return
	 */
	@AutoLog(value = "审批模板表-添加")
	@Operation(summary="审批模板表-添加")
	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates) {
		// 新增时默认设置为未激活状态
		if (emsRepairOrderApprovalTemplates.getIsActive() == null || emsRepairOrderApprovalTemplates.getIsActive().isEmpty()) {
			emsRepairOrderApprovalTemplates.setIsActive("0");
		}

		// 设置创建时间和更新时间
		Date now = new Date();
		String currentUser = ((LoginUser) SecurityUtils.getSubject().getPrincipal()).getUsername();


		emsRepairOrderApprovalTemplates.setCreateTime(now);
		emsRepairOrderApprovalTemplates.setUpdateTime(now);
		emsRepairOrderApprovalTemplates.setCreateBy(currentUser);
		emsRepairOrderApprovalTemplates.setUpdateBy(currentUser);

		emsRepairOrderApprovalTemplatesService.save(emsRepairOrderApprovalTemplates);


		// 设置具体的审批模板步骤
		List<EmsRepairOrderApprovalTemplateSteps> steps = new ArrayList<>();
//		List<String> selectedRoleIds = emsRepairOrderApprovalTemplates.getSelectedRoleIds();
		List<Object> selectedRoles = emsRepairOrderApprovalTemplates.getSelectedRoles();
		for (Object selectedRole : selectedRoles) {
			LinkedHashMap role = (LinkedHashMap) selectedRole;
			EmsRepairOrderApprovalTemplateSteps step = new EmsRepairOrderApprovalTemplateSteps();
			step.setRoleCode(role.get("roleCode").toString());
			step.setStep(role.get("sortOrder").toString());
			step.setSysOrgCode(emsRepairOrderApprovalTemplates.getSysOrgCode());

			step.setTemplateId(emsRepairOrderApprovalTemplates.getId());

			steps.add(step);
		}

		emsRepairOrderApprovalTemplateStepsService.saveBatch(steps);


		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param emsRepairOrderApprovalTemplates
	 * @return
	 */
//	@AutoLog(value = "审批模板表-编辑")
//	@Operation(summary="审批模板表-编辑")
//	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:edit")
//	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
//	public Result<String> edit(@RequestBody EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates) {
//		// 设置更新时间和更新人
//		Date now = new Date();
//		String currentUser = ((LoginUser) SecurityUtils.getSubject().getPrincipal()).getUsername();
//
//		emsRepairOrderApprovalTemplates.setUpdateTime(now);
//		emsRepairOrderApprovalTemplates.setUpdateBy(currentUser);
//
//		// 如果要激活模板，需要先检查同部门是否有其他激活的模板
//		if ("1".equals(emsRepairOrderApprovalTemplates.getIsActive())) {
//			String sysOrgCode = emsRepairOrderApprovalTemplates.getSysOrgCode();
//			if (sysOrgCode == null || sysOrgCode.isEmpty()) {
//				// 如果没有部门编码，从数据库中获取
//				EmsRepairOrderApprovalTemplates existingTemplate = emsRepairOrderApprovalTemplatesService.getById(emsRepairOrderApprovalTemplates.getId());
//				if (existingTemplate != null) {
//					sysOrgCode = existingTemplate.getSysOrgCode();
//				}
//			}
//
//			if (sysOrgCode != null && !sysOrgCode.isEmpty()) {
//				// 使用激活方法确保同部门只有一个激活模板
//				boolean activateResult = emsRepairOrderApprovalTemplatesService.activateTemplate(
//					emsRepairOrderApprovalTemplates.getId(), sysOrgCode);
//				if (!activateResult) {
//					return Result.error("激活模板失败！");
//				}
//				return Result.OK("模板激活成功！");
//			}
//		}
//
//		emsRepairOrderApprovalTemplatesService.updateById(emsRepairOrderApprovalTemplates);
//		return Result.OK("编辑成功!");
//	}

	/**
	 * 激活模板, 需要有编辑权限。
	 *
	 * @param templateId 模板ID
	 * @return
	 */
	@AutoLog(value = "审批模板表-激活模板")
	@Operation(summary="审批模板表-激活模板")
	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:edit")
	@PostMapping(value = "/activate")
	public Result<String> activateTemplate(@RequestParam(name="templateId", required=true) String templateId) {
		try {
			log.info("激活模板请求，templateId: {}", templateId);

			// 获取模板信息
			EmsRepairOrderApprovalTemplates template = emsRepairOrderApprovalTemplatesService.getById(templateId);
			if (template == null) {
				return Result.error("模板不存在！");
			}

			String sysOrgCode = template.getSysOrgCode();
			if (sysOrgCode == null || sysOrgCode.isEmpty()) {
				return Result.error("模板部门信息不完整！");
			}

			// 激活模板
			boolean result = emsRepairOrderApprovalTemplatesService.activateTemplate(templateId, sysOrgCode);
			if (result) {
				return Result.OK("模板激活成功！该部门其他模板已自动设置为未激活状态。");
			} else {
				return Result.error("模板激活失败！");
			}
		} catch (Exception e) {
			log.error("激活模板异常，templateId: {}", templateId, e);
			return Result.error("激活模板失败：" + e.getMessage());
		}
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "审批模板表-通过id删除")
	@Operation(summary="审批模板表-通过id删除")
	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		emsRepairOrderApprovalTemplatesService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "审批模板表-批量删除")
	@Operation(summary="审批模板表-批量删除")
	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.emsRepairOrderApprovalTemplatesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "审批模板表-通过id查询")
	@Operation(summary="审批模板表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<EmsRepairOrderApprovalTemplates> queryById(@RequestParam(name="id",required=true) String id) {
			log.info("查询审批模板详情，ID: {}", id);

			EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates = emsRepairOrderApprovalTemplatesService.getById(id);
			if(emsRepairOrderApprovalTemplates==null) {
				log.warn("未找到ID为{}的审批模板", id);
				return Result.error("未找到对应数据");
			}

			log.info("找到审批模板: {}", emsRepairOrderApprovalTemplates.getTemplateName());

		// 查询审批步骤，按步骤序号排序
		List<EmsRepairOrderApprovalTemplateSteps> steps = emsRepairOrderApprovalTemplateStepsService.list(
			new QueryWrapper<EmsRepairOrderApprovalTemplateSteps>()
				.lambda()
				.eq(EmsRepairOrderApprovalTemplateSteps::getTemplateId, id)
				.orderByAsc(EmsRepairOrderApprovalTemplateSteps::getStep)
		);

		log.info("查询到{}个审批步骤", steps != null ? steps.size() : 0);

		// 初始化selectedRoles列表
		List<Object> selectedRoles = new ArrayList<>();
		emsRepairOrderApprovalTemplates.setSelectedRoles(selectedRoles);

		// 构建审批步骤信息
		if (steps != null && !steps.isEmpty()) {
			for (EmsRepairOrderApprovalTemplateSteps step : steps) {
				HashMap<String, Object> stepMap = new HashMap<>();
				stepMap.put("roleCode", step.getRoleCode());
				stepMap.put("sortOrder", step.getStep());

				// 查询角色名称 todo: 前端可以用注解查询字典的，但是我不了解，暂时直接手动查询了。
				EmsDepartRole one = emsDepartRoleService.getOne(new QueryWrapper<EmsDepartRole>()
						.lambda()
						.eq(EmsDepartRole::getRoleCode, step.getRoleCode()));
				stepMap.put("roleName", one.getRoleName());

				selectedRoles.add(stepMap);
			}
		}

			log.info("成功查询审批模板详情，包含{}个审批步骤", selectedRoles.size());
			return Result.OK(emsRepairOrderApprovalTemplates);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param emsRepairOrderApprovalTemplates
    */
    @RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates) {
        return super.exportXls(request, emsRepairOrderApprovalTemplates, EmsRepairOrderApprovalTemplates.class, "审批模板表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EmsRepairOrderApprovalTemplates.class);
    }

    /**
     * 查询当前用户所在部门是否有激活的模板
     *
     * @return
     */
    @Operation(summary="查询当前用户所在部门是否有激活的模板")
    @GetMapping(value = "/checkActiveTemplate")
    public Result<Map<String, Object>> checkActiveTemplate() {
            log.info("查询当前用户所在部门是否有激活的模板");

            // 获取当前登录用户信息
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                log.warn("未获取到当前登录用户信息");
                return Result.error("用户未登录");
            }

            String currentUserOrgCode = loginUser.getOrgCode();


            if (currentUserOrgCode == null) {
                log.warn("当前用户未关联部门信息");
                return Result.error("当前用户未关联部门信息");
            }

            // 查询当前用户部门是否有激活的审批模板
            List<EmsRepairOrderApprovalTemplates> activeTemplates = emsRepairOrderApprovalTemplatesService.list(
					new QueryWrapper<EmsRepairOrderApprovalTemplates>()
							.eq("sys_org_code", currentUserOrgCode)
							.eq("is_active", "1")
			);

			if(CollectionUtil.isEmpty(activeTemplates)){
				return Result.error("当前部门没有激活的模板");
			}

			if(activeTemplates.size()>1){
				return Result.error("异常，当前部门激活了多个模板");
			}

		Result<EmsRepairOrderApprovalTemplates> result = this.queryById(activeTemplates.get(0).getId());

		return Result.ok(BeanUtil.beanToMap(result.getResult()));
    }

}
